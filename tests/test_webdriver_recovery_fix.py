#!/usr/bin/env python3
"""
Test suite for WebDriver recovery mechanism fix.

This test suite verifies that the WebDriver initialization error has been fixed
and that driver recovery works correctly without the deprecated 'desired_capabilities' error.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the app directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'app_android'))

class TestWebDriverRecoveryFix(unittest.TestCase):
    """Test cases for WebDriver recovery mechanism fix."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_logger = Mock()
        self.mock_socketio = Mock()
        
        # Mock device IDs
        self.ios_device_id = "test-ios-device-123"
        self.android_device_id = "test-android-device-456"

    def create_mock_ios_controller(self):
        """Create a mock iOS device controller for testing."""
        from utils.appium_device_controller import AppiumDeviceController
        
        controller = AppiumDeviceController(
            device_id=self.ios_device_id,
            appium_port=4723,
            wda_port=8100,
            logger=self.mock_logger,
            socketio=self.mock_socketio
        )
        controller.platform_name = 'iOS'
        controller.appium_server_url = f"http://127.0.0.1:4723/wd/hub"
        return controller

    def create_mock_android_controller(self):
        """Create a mock Android device controller for testing."""
        from utils.appium_device_controller import AppiumDeviceController
        
        controller = AppiumDeviceController(
            device_id=self.android_device_id,
            appium_port=4724,
            wda_port=8101,
            logger=self.mock_logger,
            socketio=self.mock_socketio
        )
        controller.platform_name = 'Android'
        controller.appium_server_url = f"http://127.0.0.1:4724/wd/hub"
        return controller

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_ios_recovery_uses_modern_options_parameter(self, mock_appium_options, mock_webdriver_remote):
        """Test that iOS recovery method uses modern AppiumOptions instead of deprecated desired_capabilities."""
        # Setup mocks
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_driver = Mock()
        mock_driver.get_screenshot_as_base64.return_value = "fake_screenshot_data"
        mock_webdriver_remote.return_value = mock_driver
        
        # Create controller
        controller = self.create_mock_ios_controller()
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify AppiumOptions was used
        mock_appium_options.assert_called_once()
        
        # Verify webdriver.Remote was called with options parameter (not desired_capabilities)
        mock_webdriver_remote.assert_called_once()
        call_args = mock_webdriver_remote.call_args
        
        # Check that 'options' parameter was used
        self.assertIn('options', call_args.kwargs)
        self.assertEqual(call_args.kwargs['options'], mock_options_instance)
        
        # Check that deprecated 'desired_capabilities' parameter was NOT used
        self.assertNotIn('desired_capabilities', call_args.kwargs)
        
        # Verify recovery was successful
        self.assertTrue(result)

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_android_recovery_uses_modern_options_parameter(self, mock_appium_options, mock_webdriver_remote):
        """Test that Android recovery method uses modern AppiumOptions instead of deprecated desired_capabilities."""
        # Setup mocks
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_driver = Mock()
        mock_driver.get_screenshot_as_base64.return_value = "fake_screenshot_data"
        mock_webdriver_remote.return_value = mock_driver
        
        # Create controller
        controller = self.create_mock_android_controller()
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify AppiumOptions was used
        mock_appium_options.assert_called_once()
        
        # Verify webdriver.Remote was called with options parameter (not desired_capabilities)
        mock_webdriver_remote.assert_called_once()
        call_args = mock_webdriver_remote.call_args
        
        # Check that 'options' parameter was used
        self.assertIn('options', call_args.kwargs)
        self.assertEqual(call_args.kwargs['options'], mock_options_instance)
        
        # Check that deprecated 'desired_capabilities' parameter was NOT used
        self.assertNotIn('desired_capabilities', call_args.kwargs)
        
        # Verify recovery was successful
        self.assertTrue(result)

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_ios_recovery_handles_capabilities_transfer_correctly(self, mock_appium_options, mock_webdriver_remote):
        """Test that iOS recovery correctly transfers capabilities to AppiumOptions."""
        # Setup mocks
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_driver = Mock()
        mock_driver.get_screenshot_as_base64.return_value = "fake_screenshot_data"
        mock_webdriver_remote.return_value = mock_driver
        
        # Create controller
        controller = self.create_mock_ios_controller()
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify that set_capability was called for each expected iOS capability
        expected_capabilities = [
            'platformName', 'deviceName', 'udid', 'automationName',
            'newCommandTimeout', 'wdaLaunchTimeout', 'wdaConnectionTimeout',
            'noReset', 'fullReset'
        ]
        
        # Check that set_capability was called multiple times
        self.assertGreater(mock_options_instance.set_capability.call_count, 0)
        
        # Verify recovery was successful
        self.assertTrue(result)

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_android_recovery_handles_capabilities_transfer_correctly(self, mock_appium_options, mock_webdriver_remote):
        """Test that Android recovery correctly transfers capabilities to AppiumOptions."""
        # Setup mocks
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_driver = Mock()
        mock_driver.get_screenshot_as_base64.return_value = "fake_screenshot_data"
        mock_webdriver_remote.return_value = mock_driver
        
        # Create controller
        controller = self.create_mock_android_controller()
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify that set_capability was called for each expected Android capability
        expected_capabilities = [
            'platformName', 'deviceName', 'udid', 'automationName',
            'newCommandTimeout', 'noReset', 'fullReset'
        ]
        
        # Check that set_capability was called multiple times
        self.assertGreater(mock_options_instance.set_capability.call_count, 0)
        
        # Verify recovery was successful
        self.assertTrue(result)

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_recovery_handles_driver_quit_gracefully(self, mock_appium_options, mock_webdriver_remote):
        """Test that recovery handles existing driver quit gracefully."""
        # Setup mocks
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_new_driver = Mock()
        mock_new_driver.get_screenshot_as_base64.return_value = "fake_screenshot_data"
        mock_webdriver_remote.return_value = mock_new_driver
        
        # Create controller with existing driver
        controller = self.create_mock_ios_controller()
        mock_old_driver = Mock()
        controller.driver = mock_old_driver
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify old driver was quit
        mock_old_driver.quit.assert_called_once()
        
        # Verify new driver was created
        mock_webdriver_remote.assert_called_once()
        
        # Verify recovery was successful
        self.assertTrue(result)

    @patch('utils.appium_device_controller.webdriver.Remote')
    @patch('utils.appium_device_controller.AppiumOptions')
    def test_recovery_handles_webdriver_exceptions(self, mock_appium_options, mock_webdriver_remote):
        """Test that recovery handles WebDriver exceptions gracefully."""
        # Setup mocks to simulate WebDriver exception
        mock_options_instance = Mock()
        mock_appium_options.return_value = mock_options_instance
        mock_webdriver_remote.side_effect = Exception("WebDriver connection failed")
        
        # Create controller
        controller = self.create_mock_ios_controller()
        
        # Call recovery method
        result = controller.recover_connection()
        
        # Verify recovery failed gracefully
        self.assertFalse(result)
        
        # Verify error was logged
        self.mock_logger.error.assert_called()

    def test_no_desired_capabilities_parameter_in_recovery_method(self):
        """Test that the recovery method source code doesn't contain deprecated desired_capabilities parameter."""
        # Read the iOS controller source code
        ios_controller_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'app', 'utils', 'appium_device_controller.py')
        
        with open(ios_controller_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Check that desired_capabilities parameter is not used in webdriver.Remote calls
        lines = source_code.split('\n')
        webdriver_remote_lines = [line for line in lines if 'webdriver.Remote(' in line]
        
        for line in webdriver_remote_lines:
            # Check the next few lines for parameters
            line_index = lines.index(line)
            parameter_block = '\n'.join(lines[line_index:line_index+5])
            
            # Verify that desired_capabilities is not used as a parameter
            self.assertNotIn('desired_capabilities=', parameter_block, 
                           f"Found deprecated desired_capabilities parameter in: {parameter_block}")

if __name__ == '__main__':
    unittest.main()
