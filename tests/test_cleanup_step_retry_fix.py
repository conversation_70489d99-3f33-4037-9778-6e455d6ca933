#!/usr/bin/env python3
"""
Comprehensive test suite for cleanup step execution during test suite retries.
Tests the fix for the bug where cleanup steps were skipped during retry scenarios.

The bug was: During test suite execution with retry enabled, when a test case failed
and was retried, cleanup steps were skipped because has_step_failures was reset to False
at the beginning of each play() method call.

The fix: Cleanup steps now ALWAYS execute when they exist, regardless of test success/failure
or retry status.
"""

import unittest
import os
import sys
import json
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the player modules
from app.utils.player import Player
from app_android.utils.player import Player as AndroidPlayer

class TestCleanupStepRetryFix(unittest.TestCase):
    """Test suite for cleanup step execution during retries."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock device controller
        self.mock_device_controller = Mock()
        self.mock_device_controller.tap.return_value = True
        self.mock_device_controller.get_screenshot.return_value = "mock_screenshot.png"
        
        # Create mock socketio
        self.mock_socketio = Mock()
        
        # Create mock logger
        self.mock_logger = Mock()
        
        # Create players for both iOS and Android
        self.ios_player = Player(device_controller=self.mock_device_controller)
        self.ios_player.socketio = self.mock_socketio
        self.ios_player.logger = self.mock_logger
        
        self.android_player = AndroidPlayer(device_controller=self.mock_device_controller)
        self.android_player.socketio = self.mock_socketio
        self.android_player.logger = self.mock_logger
        
        # Mock device connection methods
        self.ios_player._verify_device_connection = Mock(return_value=True)
        self.ios_player._recover_device_connection = Mock(return_value=True)
        self.android_player._verify_device_connection = Mock(return_value=True)
        self.android_player._recover_device_connection = Mock(return_value=True)
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_actions_with_cleanup(self, regular_actions_count=3, cleanup_actions_count=2, 
                                       should_fail_at_step=None):
        """
        Create test actions with cleanup steps.
        
        Args:
            regular_actions_count: Number of regular actions to create
            cleanup_actions_count: Number of cleanup actions to create
            should_fail_at_step: Step number that should fail (1-based), None for all success
        
        Returns:
            List of action dictionaries
        """
        actions = []
        
        # Create regular actions
        for i in range(regular_actions_count):
            action = {
                'type': 'tap',
                'action_id': f'action_{i+1}',
                'x': 100 + i * 10,
                'y': 200 + i * 10,
                'timestamp': i * 1000
            }
            actions.append(action)
        
        # Create cleanup actions
        for i in range(cleanup_actions_count):
            cleanup_action = {
                'type': 'cleanupSteps',
                'test_case_id': f'cleanup_test_case_{i+1}',
                'test_case_steps': [
                    {
                        'type': 'tap',
                        'action_id': f'cleanup_action_{i+1}',
                        'x': 300 + i * 10,
                        'y': 400 + i * 10
                    }
                ]
            }
            actions.append(cleanup_action)
        
        return actions, should_fail_at_step
    
    def mock_execute_action_with_failure(self, should_fail_at_step):
        """
        Create a mock execute_action method that fails at a specific step.
        
        Args:
            should_fail_at_step: Step number that should fail (1-based), None for all success
        """
        self.action_call_count = 0
        
        def mock_execute_action(action):
            self.action_call_count += 1
            action_type = action.get('type', 'unknown')
            
            # Cleanup steps should always succeed
            if action_type == 'cleanupSteps':
                return True, "Cleanup step executed successfully"
            
            # Regular actions - fail at specified step
            if should_fail_at_step and self.action_call_count == should_fail_at_step:
                return False, f"Action {self.action_call_count} failed intentionally"
            
            return True, f"Action {self.action_call_count} executed successfully"
        
        return mock_execute_action
    
    def test_cleanup_steps_execute_with_no_retries_success(self):
        """Test cleanup steps execute when retry count = 0 and test passes."""
        # Create test actions with cleanup steps
        actions, _ = self.create_test_actions_with_cleanup(3, 2, None)
        
        # Mock execute_action to always succeed
        self.ios_player.execute_action = self.mock_execute_action_with_failure(None)
        
        # Execute the test
        success, message = self.ios_player.play(actions)
        
        # Verify test passed
        self.assertTrue(success)
        
        # Verify cleanup steps were executed (should be called for all actions)
        self.assertEqual(self.action_call_count, 5)  # 3 regular + 2 cleanup
        
        # Verify logger was called with cleanup execution messages
        cleanup_log_calls = [call for call in self.mock_logger.info.call_args_list 
                           if 'cleanup' in str(call).lower()]
        self.assertGreater(len(cleanup_log_calls), 0, "Cleanup steps should have been logged")
    
    def test_cleanup_steps_execute_with_no_retries_failure(self):
        """Test cleanup steps execute when retry count = 0 and test fails."""
        # Create test actions with cleanup steps
        actions, _ = self.create_test_actions_with_cleanup(3, 2, 2)  # Fail at step 2
        
        # Mock execute_action to fail at step 2
        self.ios_player.execute_action = self.mock_execute_action_with_failure(2)
        
        # Execute the test
        success, message = self.ios_player.play(actions)
        
        # Verify test failed
        self.assertFalse(success)
        
        # Verify cleanup steps were executed (2 regular actions + 2 cleanup)
        # Note: execution stops at failure but cleanup still runs
        self.assertGreaterEqual(self.action_call_count, 3)  # At least 2 regular + cleanup
        
        # Verify logger was called with cleanup execution messages
        cleanup_log_calls = [call for call in self.mock_logger.info.call_args_list
                           if 'cleanup' in str(call).lower()]
        self.assertGreater(len(cleanup_log_calls), 0, "Cleanup steps should have been logged")

    def test_cleanup_steps_execute_during_test_suite_retries(self):
        """Test cleanup steps execute during test suite execution with retries."""
        # Create test actions with cleanup steps
        actions, _ = self.create_test_actions_with_cleanup(3, 2, 2)  # Fail at step 2

        # Mock execute_action to fail on first attempt, succeed on second
        self.retry_attempt = 0

        def mock_execute_action_with_retry(action):
            action_type = action.get('type', 'unknown')

            # Cleanup steps should always succeed
            if action_type == 'cleanupSteps':
                return True, "Cleanup step executed successfully"

            # Regular actions - fail on first attempt at step 2, succeed on retry
            if action.get('action_id') == 'action_2':
                self.retry_attempt += 1
                if self.retry_attempt == 1:
                    return False, "Action 2 failed on first attempt"
                else:
                    return True, "Action 2 succeeded on retry"

            return True, "Action executed successfully"

        self.ios_player.execute_action = mock_execute_action_with_retry

        # Simulate test suite retry logic
        max_retries = 1
        retry_count = 0
        success = False

        while retry_count <= max_retries and not success:
            # Reset the player state for retry (simulating test suite behavior)
            self.ios_player.is_playing = False
            self.ios_player.execution_stopped = False

            # Execute the test
            success, message = self.ios_player.play(actions)

            if not success and retry_count < max_retries:
                retry_count += 1
            else:
                break

        # Verify test eventually passed after retry
        self.assertTrue(success, "Test should pass after retry")

        # Verify cleanup steps were executed in both attempts
        # The cleanup should have been called at least twice (once per attempt)
        cleanup_log_calls = [call for call in self.mock_logger.info.call_args_list
                           if 'cleanup' in str(call).lower()]
        self.assertGreater(len(cleanup_log_calls), 0, "Cleanup steps should have been logged during retries")

    def test_android_cleanup_steps_execute_with_retries(self):
        """Test cleanup steps execute in Android player during retries."""
        # Create test actions with cleanup steps
        actions, _ = self.create_test_actions_with_cleanup(3, 2, 2)  # Fail at step 2

        # Mock execute_action to fail at step 2
        self.android_player.execute_action = self.mock_execute_action_with_failure(2)

        # Execute the test
        success, message = self.android_player.play(actions)

        # Verify test failed
        self.assertFalse(success)

        # Verify cleanup steps were executed
        cleanup_log_calls = [call for call in self.mock_logger.info.call_args_list
                           if 'cleanup' in str(call).lower()]
        self.assertGreater(len(cleanup_log_calls), 0, "Android cleanup steps should have been logged")

    def test_cleanup_steps_always_execute_regardless_of_failure_flag(self):
        """Test that cleanup steps execute regardless of has_step_failures flag."""
        # Create test actions with cleanup steps
        actions, _ = self.create_test_actions_with_cleanup(3, 2, None)  # All succeed

        # Mock execute_action to always succeed
        self.ios_player.execute_action = self.mock_execute_action_with_failure(None)

        # Execute the test
        success, message = self.ios_player.play(actions)

        # Verify test passed
        self.assertTrue(success)

        # Verify cleanup steps were executed even though no failures occurred
        cleanup_log_calls = [call for call in self.mock_logger.info.call_args_list
                           if 'cleanup' in str(call).lower()]
        self.assertGreater(len(cleanup_log_calls), 0, "Cleanup steps should execute even without failures")

        # Verify the specific log message indicating cleanup steps always run
        always_run_logs = [call for call in self.mock_logger.info.call_args_list
                          if 'always run regardless of test status' in str(call)]
        self.assertGreater(len(always_run_logs), 0, "Should log that cleanup steps always run")

    def test_no_cleanup_steps_scenario(self):
        """Test behavior when no cleanup steps are present."""
        # Create test actions without cleanup steps
        actions = []
        for i in range(3):
            action = {
                'type': 'tap',
                'action_id': f'action_{i+1}',
                'x': 100 + i * 10,
                'y': 200 + i * 10,
                'timestamp': i * 1000
            }
            actions.append(action)

        # Mock execute_action to always succeed
        self.ios_player.execute_action = self.mock_execute_action_with_failure(None)

        # Execute the test
        success, message = self.ios_player.play(actions)

        # Verify test passed
        self.assertTrue(success)

        # Verify appropriate log message for no cleanup steps
        no_cleanup_logs = [call for call in self.mock_logger.info.call_args_list
                          if 'No cleanup steps found' in str(call)]
        self.assertGreater(len(no_cleanup_logs), 0, "Should log when no cleanup steps are found")


if __name__ == '__main__':
    unittest.main()
